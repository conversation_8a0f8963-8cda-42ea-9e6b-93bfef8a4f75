<template>
  <nav class="backdrop-blur-md px-6 py-4 fixed top-0 left-0 right-0 z-50">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <!-- Logo -->
      <div class="text-2xl font-bold text-white hover:text-gray-300 cursor-pointer">SHINJAGIRA</div>

      <!-- Desktop Nav Links -->
      <ul class="hidden md:flex space-x-8 text-white font-medium text-base">
        <li><a href="#about" class="hover:text-gray-300 transition">About Me</a></li>
        <li><a href="#stacks" class="hover:text-gray-300 transition">My Stacks</a></li>
        <li><a href="#experience" class="hover:text-gray-300 transition">Experience</a></li>
        <li><a href="#contact" class="hover:text-gray-300 transition">Contact Me</a></li>
      </ul>

      <!-- Mobile Menu Toggle -->
      <button @click="isOpen = !isOpen" class="md:hidden text-white focus:outline-none">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            :class="{ hidden: isOpen, 'inline-flex': !isOpen }"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          />
          <path
            :class="{ 'inline-flex': isOpen, hidden: !isOpen }"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Mobile Menu -->
    <div v-if="isOpen" class="md:hidden mt-4 space-y-4 text-white font-medium text-base">
      <a href="#about" @click="isOpen = false" class="block hover:text-gray-300 transition"
        >About Me</a
      >
      <a href="#projects" @click="isOpen = false" class="block hover:text-gray-300 transition"
        >My Projects</a
      >
      <a href="#experience" @click="isOpen = false" class="block hover:text-gray-300 transition"
        >Experience</a
      >
      <a href="#contact" @click="isOpen = false" class="block hover:text-gray-300 transition"
        >Contact Me</a
      >

      <!-- Language Switcher (Mobile) -->
      <div class="pt-4 border-t border-gray-600">
        <div class="hover:text-gray-300 cursor-pointer">English</div>
        <div class="hover:text-gray-300 cursor-pointer">Kinyarwanda</div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref } from 'vue'
const isOpen = ref(false)
</script>

<style>
html {
  scroll-behavior: smooth;
  scroll-padding-top: 70px;
}
</style>
