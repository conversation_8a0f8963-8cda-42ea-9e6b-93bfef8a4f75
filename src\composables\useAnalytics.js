import { ref } from 'vue'

export function useAnalytics() {
  const isInitialized = ref(false)
  
  // Initialize Google Analytics 4
  const initGA4 = (measurementId) => {
    if (typeof window === 'undefined' || isInitialized.value) return
    
    // Load gtag script
    const script = document.createElement('script')
    script.async = true
    script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`
    document.head.appendChild(script)
    
    // Initialize gtag
    window.dataLayer = window.dataLayer || []
    function gtag() {
      window.dataLayer.push(arguments)
    }
    window.gtag = gtag
    
    gtag('js', new Date())
    gtag('config', measurementId, {
      page_title: document.title,
      page_location: window.location.href
    })
    
    isInitialized.value = true
  }
  
  // Track page views
  const trackPageView = (pagePath, pageTitle) => {
    if (typeof window.gtag !== 'function') return
    
    window.gtag('config', 'GA_MEASUREMENT_ID', {
      page_path: pagePath,
      page_title: pageTitle
    })
  }
  
  // Track events
  const trackEvent = (eventName, parameters = {}) => {
    if (typeof window.gtag !== 'function') return
    
    window.gtag('event', eventName, parameters)
  }
  
  // Track section views
  const trackSectionView = (sectionName) => {
    trackEvent('section_view', {
      section_name: sectionName,
      page_location: window.location.href
    })
  }
  
  // Track contact form submissions
  const trackContactForm = (method) => {
    trackEvent('contact_form_submit', {
      contact_method: method,
      page_location: window.location.href
    })
  }
  
  // Track technology stack interactions
  const trackTechStackClick = (technology) => {
    trackEvent('tech_stack_click', {
      technology_name: technology,
      page_location: window.location.href
    })
  }
  
  // Track social media clicks
  const trackSocialClick = (platform, url) => {
    trackEvent('social_click', {
      social_platform: platform,
      link_url: url,
      page_location: window.location.href
    })
  }
  
  return {
    initGA4,
    trackPageView,
    trackEvent,
    trackSectionView,
    trackContactForm,
    trackTechStackClick,
    trackSocialClick,
    isInitialized
  }
}
