<template>
  <section id="contact" class="max-w-4xl mx-auto py-16 text-white">
    <h2 class="text-3xl font-bold mb-4 text-center">Contact Me</h2>
    <p class="text-gray-400 mb-10 text-center">
      Let's connect! Feel free to reach out for collaborations, questions, or just to say hi.
    </p>

    <div class="px-4 md:px-6 rounded-lg shadow-lg">
      <form ref="contactForm" @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Name -->
        <div>
          <label class="block mb-2 text-sm font-medium text-gray-300">Your Name</label>
          <input
            type="text"
            v-model="form.user_name"
            name='user_name'
            required  
            placeholder="Enter your name"
            class="w-full px-4 py-2 rounded bg-black border border-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500"
          />
        </div>

        <!-- Email -->
        <div>
          <label class="block mb-2 text-sm font-medium text-gray-300">Email Address</label>
          <input
            type="email"
            v-model="form.user_email"
            name='user_email'
            required
            placeholder="<EMAIL>"
            class="w-full px-4 py-2 rounded bg-black border border-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500"
          />
        </div>

        <!-- Message -->
        <div>
          <label class="block mb-2 text-sm font-medium text-gray-300">Message</label>
          <textarea
            v-model="form.user_message"
            name='user_message'
            required
            rows="5"
            placeholder="Type your message here..."
            class="w-full px-4 py-2 rounded bg-black border border-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500"
          ></textarea>
        </div>

        <!-- Submit Button -->
        <div class="text-center">
  <button
    type="submit"
    :disabled="isSubmitting"
    class="px-6 py-2 rounded-lg cursor-pointer w-full font-semibold transition duration-300
      bg-white text-black hover:bg-black hover:text-white
      disabled:opacity-50 disabled:cursor-not-allowed"
  >
    <span v-if="isSubmitting">Sending...</span>
    <span v-else>Send Message</span>
  </button>

        </div>
      </form>
    </div>
  </section>
</template>

<script setup>
import { reactive, ref } from 'vue'
import emailjs from '@emailjs/browser'

// Reactive form state
const form = reactive({
  user_name: '',
  user_email: '',
  user_message: '',
})
const isSubmitting = ref(false)

// Reference to the form element
const contactForm = ref(null)

// Form submission handler
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    await emailjs.sendForm('service_fv2q4du', 'template_8su13sr', contactForm.value, 'rLqvzTrI8HbbQsqzK')
    alert(`Thank you ${form.user_name}, your message has been sent!. We will get back to you as soon as possible.`)
    form.user_name = ''
    form.user_email = ''
    form.user_message = ''
  } catch (error) {
    console.error('EmailJS error:', error)
    alert('Oops! Something went wrong. Please try again later.')
  } finally {
    isSubmitting.value = false
  }
}
</script>
