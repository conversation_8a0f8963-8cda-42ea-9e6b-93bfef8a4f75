@echo off
echo Building the Vue.js application...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    call npm install
    echo.
)

echo Running build command...
call npm run build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build completed successfully!
    echo The dist folder contains your production-ready files.
) else (
    echo.
    echo ❌ Build failed with error code %ERRORLEVEL%
    echo Please check the error messages above.
)

echo.
pause
