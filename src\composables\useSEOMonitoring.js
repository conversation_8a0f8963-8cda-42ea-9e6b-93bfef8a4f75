import { ref, onMounted } from 'vue'

export function useSEOMonitoring() {
  const seoHealth = ref({
    score: 0,
    issues: [],
    recommendations: []
  })

  const checkSEOHealth = () => {
    const issues = []
    const recommendations = []
    let score = 100

    // Check meta tags
    if (!document.querySelector('meta[name="description"]')) {
      issues.push('Missing meta description')
      score -= 15
    }

    if (!document.querySelector('meta[name="keywords"]')) {
      issues.push('Missing meta keywords')
      score -= 10
    }

    // Check Open Graph tags
    if (!document.querySelector('meta[property="og:title"]')) {
      issues.push('Missing Open Graph title')
      score -= 10
    }

    if (!document.querySelector('meta[property="og:image"]')) {
      issues.push('Missing Open Graph image')
      score -= 10
    }

    // Check structured data
    if (!document.querySelector('script[type="application/ld+json"]')) {
      issues.push('Missing structured data')
      score -= 15
    }

    // Check canonical URL
    if (!document.querySelector('link[rel="canonical"]')) {
      issues.push('Missing canonical URL')
      score -= 10
    }

    // Check page title
    if (!document.title || document.title.length < 30) {
      issues.push('Page title too short (should be 30-60 characters)')
      score -= 10
    }

    if (document.title && document.title.length > 60) {
      issues.push('Page title too long (should be 30-60 characters)')
      score -= 5
    }

    // Check meta description
    const metaDesc = document.querySelector('meta[name="description"]')
    if (metaDesc) {
      const descLength = metaDesc.getAttribute('content').length
      if (descLength < 120) {
        recommendations.push('Meta description could be longer (120-160 characters)')
      }
      if (descLength > 160) {
        issues.push('Meta description too long (should be 120-160 characters)')
        score -= 5
      }
    }

    // Check headings
    const h1Count = document.querySelectorAll('h1').length
    if (h1Count === 0) {
      issues.push('Missing H1 heading')
      score -= 15
    } else if (h1Count > 1) {
      issues.push('Multiple H1 headings found (should have only one)')
      score -= 10
    }

    // Performance recommendations
    recommendations.push('Monitor Core Web Vitals in Google Search Console')
    recommendations.push('Set up Google Analytics for traffic monitoring')
    recommendations.push('Submit sitemap to Google Search Console')

    seoHealth.value = {
      score: Math.max(0, score),
      issues,
      recommendations
    }

    return seoHealth.value
  }

  const logSEOReport = () => {
    const health = checkSEOHealth()
    
    console.group('🔍 SEO Health Report')
    console.log(`📊 SEO Score: ${health.score}/100`)
    
    if (health.issues.length > 0) {
      console.group('❌ Issues Found:')
      health.issues.forEach(issue => console.log(`• ${issue}`))
      console.groupEnd()
    }
    
    if (health.recommendations.length > 0) {
      console.group('💡 Recommendations:')
      health.recommendations.forEach(rec => console.log(`• ${rec}`))
      console.groupEnd()
    }
    
    console.groupEnd()
    
    return health
  }

  // Track page performance
  const trackPagePerformance = () => {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const perfData = performance.getEntriesByType('navigation')[0]
          const loadTime = perfData.loadEventEnd - perfData.loadEventStart
          const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart
          
          console.group('⚡ Performance Metrics')
          console.log(`Page Load Time: ${loadTime.toFixed(2)}ms`)
          console.log(`DOM Content Loaded: ${domContentLoaded.toFixed(2)}ms`)
          console.log(`Total Page Load: ${(perfData.loadEventEnd - perfData.fetchStart).toFixed(2)}ms`)
          console.groupEnd()
          
          // Track with analytics if available
          if (window.gtag) {
            window.gtag('event', 'page_load_time', {
              value: Math.round(loadTime),
              custom_parameter: 'performance_monitoring'
            })
          }
        }, 0)
      })
    }
  }

  onMounted(() => {
    // Run SEO check after component mounts
    setTimeout(() => {
      logSEOReport()
      trackPagePerformance()
    }, 1000)
  })

  return {
    seoHealth,
    checkSEOHealth,
    logSEOReport,
    trackPagePerformance
  }
}
