<script setup>
import { RouterLink, RouterView } from 'vue-router'
import Sidebar from '../components/Sidebar.vue'
import Card from '../components/Card.vue'  
import Stack from '../components/Stack.vue'
import Tools from '../components/Tools.vue'
import Experience from '../components/Experience.vue'
import Contact from '../components/Contact.vue'
import footerComponent from '../components/footer.vue'
</script>

<template>
  <div class="lg:px-52 xl:px-72 md:px-32 px-4 space-y-4">  
    <Card />
    <Stack/>
    <Tools/>
    <Experience/>
    <Contact/>
    <footerComponent />
</div>
</template>
