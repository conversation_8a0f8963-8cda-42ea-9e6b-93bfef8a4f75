@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');
@import "tailwindcss";


@theme {
  --font-poppins: "Poppins", sans-serif;
  --font-roboto:'Roboto', sans-serif;
  --font-noto:"Noto Sans", sans-serif;
}


.animated-grid {
  background-image: linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 35px 35px;
  animation: moveGrid 10s linear infinite;
}

@keyframes moveGrid {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 100px 100px;
  }
}
