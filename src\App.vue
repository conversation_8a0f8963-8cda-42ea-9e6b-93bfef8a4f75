<script setup>
import { RouterLink, RouterView } from 'vue-router'
import Sidebar from './components/Sidebar.vue'
import Card from './components/Card.vue'  
import Stack from './components/Stack.vue'
import Tools from './components/Tools.vue'
import Experience from './components/Experience.vue'
import Contact from './components/Contact.vue'
import footerComponent from './components/footer.vue'
import { Analytics } from '@vercel/analytics/vue';
</script>

<template>
  <Analytics/>
  <div class="animated-grid font-noto  w-full text-white">
  <header>
    <div class="mb-4 py-3">
      <Sidebar />
    </div>
  </header>
  <main class="">
   <HomeView/>
  </main>
  <RouterView/>
</div>
</template>
