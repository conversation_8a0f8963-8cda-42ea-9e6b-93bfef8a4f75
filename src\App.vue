<script setup>
import { RouterLink, RouterView } from 'vue-router'
import { onMounted } from 'vue'
import Sidebar from './components/Sidebar.vue'
import HomeView from './views/HomeView.vue'
import { Analytics } from '@vercel/analytics/vue'
import { useSEO } from './composables/useSEO.js'
import { useAnalytics } from './composables/useAnalytics.js'

const { updateMetaTags, addStructuredData } = useSEO()
const { initGA4 } = useAnalytics()

onMounted(() => {
  // Initialize SEO meta tags
  updateMetaTags()

  // Add structured data for the person/organization
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: 'SHINJAGIRA SHEMA Arnaud',
    alternateName: 'She<PERSON> Arnaud',
    description:
      '18-year-old Software Engineer specializing in backend systems and mobile development',
    url: 'https://shinjxgira.vercel.app/',
    image: 'https://shinjxgira.vercel.app/logo.png',
    sameAs: ['https://github.com/shinjagira', 'https://instagram.com/shinjxgira'],
    jobTitle: 'Software Engineer',
    worksFor: {
      '@type': 'Organization',
      name: 'Freelance',
    },
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'Kigali',
      addressCountry: 'Rwanda',
    },
    email: '<EMAIL>',
    knowsAbout: [
      'Backend Development',
      'Mobile Development',
      'Node.js',
      'Django',
      'Laravel',
      'Flutter',
      'Microservices',
      'API Development',
    ],
  }

  addStructuredData(structuredData)

  // Initialize Google Analytics (replace with your actual GA4 measurement ID)
  // initGA4('G-XXXXXXXXXX')
})
</script>

<template>
  <Analytics />
  <div class="animated-grid font-noto w-full text-white">
    <header>
      <div class="mb-4 py-3">
        <Sidebar />
      </div>
    </header>
    <main class="">
      <HomeView />
    </main>
    <RouterView />
  </div>
</template>
