<template>
  <section id="about" class="mb-10 mt-20" aria-labelledby="about-heading">
    <div class="p-6 md:p-8 rounded-2xl">
      <div
        class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4"
      >
        <!-- Personal Info -->
        <header>
          <h1 id="about-heading" class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            {{ name }}
          </h1>
          <p class="text-sm text-gray-500 dark:text-gray-400" role="doc-subtitle">{{ title }}</p>
          <address class="text-gray-400 not-italic">
            <i class="pi pi-map-marker" aria-hidden="true"></i>
            <span>Kigali, Rwanda</span>
          </address>
        </header>

        <!-- Contact Icons -->
        <nav aria-label="Social media links" class="flex space-x-4 justify-start md:justify-end">
          <a
            v-for="(contact, index) in contacts"
            :key="index"
            :href="contact.url"
            target="_blank"
            rel="noopener noreferrer"
            :aria-label="`Contact via ${contact.label}`"
            class="px-3 py-2 rounded-md bg-black dark:bg-black border border-gray-300 shadow-sm transition duration-200 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
          >
            <i :class="`pi pi-${contact.icon}`" :aria-label="contact.label"></i>
          </a>
        </nav>
      </div>

      <!-- Description -->
      <div class="mt-4">
        <h2 class="sr-only">About Me</h2>
        <p class="text-gray-700 dark:text-gray-300">
          {{ description }}
        </p>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'ProfileCard',
  props: {
    name: {
      type: String,
      default: 'Shema Arnaud',
    },
    title: {
      type: String,
      default: 'Software Engineer • Backend & Mobile Dev',
    },
    description: {
      type: String,
      default:
        'HI 👋 i am Arnaud,I am 18 years old developer. I love building scalable and high performance microservices backend systems. Currently learning C and exploring Flutter for future projects.',
    },
    contacts: {
      type: Array,
      default: () => [
        {
          label: 'Email',
          url: 'mailto:<EMAIL>',
          icon: 'envelope',
        },
        {
          label: 'Instagram',
          url: 'https://instagram.com/shinjxgira',
          icon: 'instagram',
        },
        {
          label: 'GitHub',
          url: 'https://github.com/shinjagira',
          icon: 'github',
        },
      ],
    },
  },
}
</script>
