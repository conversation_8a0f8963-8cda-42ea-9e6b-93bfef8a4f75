import { ref, onMounted } from 'vue'

export function useSEO() {
  const defaultMeta = {
    title: '<PERSON><PERSON> | Backend & Mobile Developer Portfolio',
    description: 'Official portfolio of SHINJAGIRA SHEMA A<PERSON>ud, an 18-year-old software engineer from Kigali, Rwanda. Specializing in scalable backend systems and mobile development with Node.js, Django, Laravel, and Flutter.',
    keywords: '<PERSON><PERSON>, SHINJAGIRA, Backend Developer, Mobile Developer, Software Engineer, Node.js, Django, Laravel, Flutter, Kigali Rwanda, Portfolio, Full Stack Developer',
    image: 'https://shinjxgira.vercel.app/logo.png',
    url: 'https://shinjxgira.vercel.app/',
    type: 'website'
  }

  const updateMetaTags = (meta = {}) => {
    const finalMeta = { ...defaultMeta, ...meta }
    
    // Update document title
    document.title = finalMeta.title
    
    // Update or create meta tags
    updateMetaTag('description', finalMeta.description)
    updateMetaTag('keywords', finalMeta.keywords)
    updateMetaTag('author', 'SHINJAGIRA SHEMA Arnaud')
    
    // Open Graph tags
    updateMetaProperty('og:title', finalMeta.title)
    updateMetaProperty('og:description', finalMeta.description)
    updateMetaProperty('og:image', finalMeta.image)
    updateMetaProperty('og:url', finalMeta.url)
    updateMetaProperty('og:type', finalMeta.type)
    updateMetaProperty('og:site_name', 'Shema Arnaud Portfolio')
    updateMetaProperty('og:locale', 'en_US')
    
    // Twitter Card tags
    updateMetaProperty('twitter:card', 'summary_large_image')
    updateMetaProperty('twitter:title', finalMeta.title)
    updateMetaProperty('twitter:description', finalMeta.description)
    updateMetaProperty('twitter:image', finalMeta.image)
    updateMetaProperty('twitter:url', finalMeta.url)
    
    // Update canonical URL
    updateCanonicalUrl(finalMeta.url)
  }

  const updateMetaTag = (name, content) => {
    let element = document.querySelector(`meta[name="${name}"]`)
    if (!element) {
      element = document.createElement('meta')
      element.setAttribute('name', name)
      document.head.appendChild(element)
    }
    element.setAttribute('content', content)
  }

  const updateMetaProperty = (property, content) => {
    let element = document.querySelector(`meta[property="${property}"]`)
    if (!element) {
      element = document.createElement('meta')
      element.setAttribute('property', property)
      document.head.appendChild(element)
    }
    element.setAttribute('content', content)
  }

  const updateCanonicalUrl = (url) => {
    let element = document.querySelector('link[rel="canonical"]')
    if (!element) {
      element = document.createElement('link')
      element.setAttribute('rel', 'canonical')
      document.head.appendChild(element)
    }
    element.setAttribute('href', url)
  }

  const setSectionMeta = (section) => {
    const sectionMeta = {
      about: {
        title: 'About Shema Arnaud | Backend & Mobile Developer',
        description: 'Learn about SHINJAGIRA SHEMA Arnaud, an 18-year-old software engineer from Kigali, Rwanda, passionate about building scalable backend systems and mobile applications.',
        url: 'https://shinjxgira.vercel.app/#about'
      },
      stacks: {
        title: 'Technology Stack | Shema Arnaud Portfolio',
        description: 'Explore the technologies and programming languages I work with: JavaScript, Node.js, Python, Django, Laravel, Flutter, MongoDB, and more.',
        url: 'https://shinjxgira.vercel.app/#stacks'
      },
      experience: {
        title: 'Experience | Shema Arnaud Portfolio',
        description: 'Discover my professional experience and projects in backend development, mobile app development, and software engineering.',
        url: 'https://shinjxgira.vercel.app/#experience'
      },
      contact: {
        title: 'Contact Shema Arnaud | Get In Touch',
        description: 'Get in touch with SHINJAGIRA SHEMA Arnaud for collaboration opportunities, project inquiries, or professional networking.',
        url: 'https://shinjxgira.vercel.app/#contact'
      }
    }

    if (sectionMeta[section]) {
      updateMetaTags(sectionMeta[section])
    }
  }

  const addStructuredData = (data) => {
    // Remove existing structured data
    const existingScript = document.querySelector('script[type="application/ld+json"]')
    if (existingScript && existingScript.id === 'dynamic-structured-data') {
      existingScript.remove()
    }

    // Add new structured data
    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.id = 'dynamic-structured-data'
    script.textContent = JSON.stringify(data)
    document.head.appendChild(script)
  }

  return {
    updateMetaTags,
    setSectionMeta,
    addStructuredData,
    defaultMeta
  }
}
